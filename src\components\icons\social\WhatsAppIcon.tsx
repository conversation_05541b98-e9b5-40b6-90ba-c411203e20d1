import { SVGProps } from 'react'

const WhatsAppIcon = (props: SVGProps<SVGSVGElement>) => {
  return (
    <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <rect width="32" height="32" rx="16" fill="#E6E7E9" />
      <path
        d="M16.002 8H15.998C11.587 8 8 11.588 8 16C8 17.75 8.564 19.372 9.523 20.689L8.526 23.661L11.601 22.678C12.866 23.516 14.375 24 16.002 24C20.413 24 24 20.411 24 16C24 11.589 20.413 8 16.002 8ZM20.657 19.297C20.464 19.842 19.698 20.294 19.087 20.426C18.669 20.515 18.123 20.586 16.285 19.824C13.934 18.85 12.42 16.461 12.302 16.306C12.189 16.151 11.352 15.041 11.352 13.893C11.352 12.745 11.935 12.186 12.17 11.946C12.363 11.749 12.682 11.659 12.988 11.659C13.087 11.659 13.176 11.664 13.256 11.668C13.491 11.678 13.609 11.692 13.764 12.063C13.957 12.528 14.427 13.676 14.483 13.794C14.54 13.912 14.597 14.072 14.517 14.227C14.442 14.387 14.376 14.458 14.258 14.594C14.14 14.73 14.028 14.834 13.91 14.98C13.802 15.107 13.68 15.243 13.816 15.478C13.952 15.708 14.422 16.475 15.114 17.091C16.007 17.886 16.731 18.14 16.99 18.248C17.183 18.328 17.413 18.309 17.554 18.159C17.733 17.966 17.954 17.646 18.179 17.331C18.339 17.105 18.541 17.077 18.753 17.157C18.969 17.232 20.112 17.797 20.347 17.914C20.582 18.032 20.737 18.088 20.794 18.187C20.85 18.286 20.85 18.751 20.657 19.297Z"
        fill="#4B5563"
      />
    </svg>
  )
}

export { WhatsAppIcon }
