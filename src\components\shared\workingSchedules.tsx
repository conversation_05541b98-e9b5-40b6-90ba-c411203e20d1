import { cn } from '@/lib/utils'
import { ComponentProps } from 'react'

interface Props {
  title?: string
  doctorSchedule: string[]
  className?: ComponentProps<'div'>['className']
  listClassName?: ComponentProps<'ul'>['className']
  itemClassName?: ComponentProps<'li'>['className']
  titleClassName?: ComponentProps<'h5'>['className']
}

const WorkingSchedules = ({
  doctorSchedule,
  listClassName,
  itemClassName,
  title,
  titleClassName,
  className,
}: Props) => {
  return (
    <div className={cn('flex flex-col', className)}>
      {title && <h5 className={cn('text-primary-01 font-bold md:text-lg text-base', titleClassName)}>{title}</h5>}

      <ul className={cn('list-inside list-disc', listClassName)}>
        {doctorSchedule.map((schedule) => (
          <li key={schedule} className={cn('text-primary-03 text-lg', itemClassName)}>
            {schedule}
          </li>
        ))}
      </ul>
    </div>
  )
}

export { WorkingSchedules }
