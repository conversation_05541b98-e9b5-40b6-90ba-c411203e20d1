import Image from 'next/image'
import Link from 'next/link'
import { ChevronRight } from 'lucide-react'
import { IArticle } from '@/types'
import { useTranslations } from 'next-intl'
import XImage from '/public/icons/x.svg'
import FacebookImage from '/public/icons/facebook.svg'
import InstagramImage from '/public/icons/insta.svg'

const Article = ({ date, title, image, id }: IArticle) => {
  const t = useTranslations()
  const shareUrl = encodeURIComponent('https://example.com')
  const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${shareUrl}`
  const xUrl = `https://twitter.com/intent/tweet?url=${shareUrl}`
  const instagramUrl = 'https://www.instagram.com'

  return (
    <div className="bg-white rounded-[24px] overflow-hidden basic_card_shadow">
      <div className="relative w-full h-[250px]">
        <Link href={`/article/${id}`}>
          <div className="relative w-full h-full">
            <Image
              src={image || '/images/img.png'}
              alt="Article Image"
              fill
              className="object-cover"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            />
          </div>
        </Link>
      </div>
      <div className="p-8">
        <div className="flex justify-between items-center flex-wrap">
          <p className="text-xl font-bold text-gray-01">{date}</p>
          <div className="flex gap-2 items-center h-fit">
            <Link href={xUrl}>
              <div className="relative w-8 h-8">
                <Image src={XImage} alt="X icon" fill className="object-contain" />
              </div>
            </Link>
            <Link href={instagramUrl}>
              <div className="relative w-8 h-8">
                <Image src={InstagramImage} alt="Instagram icon" fill className="object-contain" />
              </div>
            </Link>
            <Link href={facebookUrl}>
              <div className="relative w-8 h-8">
                <Image src={FacebookImage} alt="Facebook icon" fill className="object-contain" />
              </div>
            </Link>
          </div>
        </div>
        <h3 className="text-primary-03 text-[28px] py-6">{title}</h3>
        <Link href="#" className="text-primary-02 font-bold text-2xl flex items-center">
          <ChevronRight />
          {t('articles.read_more')}
        </Link>
      </div>
    </div>
  )
}

export default Article
