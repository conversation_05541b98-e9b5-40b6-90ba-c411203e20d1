import { SVGProps } from 'react'

const XIcon = (props: SVGProps<SVGSVGElement>) => {
  return (
    <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <rect width="32" height="32" rx="16" fill="#E6E7E9" />
      <path
        d="M17.4895 14.7749L23.3177 8H21.9366L16.8759 13.8826L12.834 8H8.17203L14.2843 16.8955L8.17203 24H9.55322L14.8974 17.7878L19.1661 24H23.828L17.4895 14.7749ZM15.5977 16.9738L14.9784 16.0881L10.0509 9.03974H12.1723L16.1489 14.728L16.7682 15.6137L21.9373 23.0075H19.8158L15.5977 16.9738Z"
        fill="#4B5563"
      />
    </svg>
  )
}

export { XIcon }
