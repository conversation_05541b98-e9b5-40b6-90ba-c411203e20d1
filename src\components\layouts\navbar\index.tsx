import { apiService } from '@/services'

// components
import { TopNav } from '@/components/layouts/navbar/components/topNav'
import { MainNav } from '@/components/layouts/navbar/components/mainNav'

async function Navbar() {
  const doctorInfo = await apiService({
    path: 'doctor-info',
    revalidate: 1800, // Cache for 24 hours - enables static generation
  })

  return (
    <nav className=" base_gradient w-full">
      <TopNav doctorInfo={doctorInfo?.data} className="max-md:hidden" />
      <MainNav />
    </nav>
  )
}

export { Navbar }
