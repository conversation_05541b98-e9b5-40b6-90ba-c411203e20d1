import { apiService } from '@/services'
import { setRequestLocale } from 'next-intl/server'
import { IPageProps } from '@/types'

export default async function Home({ params }: IPageProps) {
  const resolvedParams = await params

  // Enable static rendering by setting the request locale
  setRequestLocale(resolvedParams.lang)

  const home = await apiService({
    path: 'home',
  })

  return <h1>Home</h1>
}
