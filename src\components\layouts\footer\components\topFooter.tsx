import { Button } from '@/components/ui/button'
import { getTranslations } from 'next-intl/server'

import BG from '/public/images/footer_bg.jpg'

export default async function TopFooter() {
  const t = await getTranslations()
  return (
    <section className="relative w-full h-[457px] overflow-hidden">
      {/* Background with doctor image */}
      <div
        className="absolute inset-0 "
        style={{
          backgroundImage: `url(${BG.src})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
        }}
      >
        {/* Fallback background if image doesn't exist */}
        <div className="absolute inset-0  bg-black/20" />
      </div>

      <div className="relative z-10 container h-full flex flex-col items-center justify-center text-center lg:gap-6 gap-4">
        {/* Main heading */}
        <h5 className="text-white lg:text-4xl md:text-3xl sm:text-2xl text-xl font-bold max-w-4xl leading-tight">
          {t('footer.top_text')}
        </h5>

        <p className="text-white lg:text-[28px] md:text-xl text-lg font-normal max-w-3xl leading-relaxed">
          {t('footer.top_text_2')}
        </p>

        <Button className="rounded-full px-8 py-3 text-lg font-semibold ">{t('button.book_now')}</Button>
      </div>
    </section>
  )
}
