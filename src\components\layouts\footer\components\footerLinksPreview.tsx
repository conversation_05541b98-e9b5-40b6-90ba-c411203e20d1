import { cn } from '@/lib/utils'
import Link from 'next/link'
import { ComponentProps } from 'react'

interface IFooterLinksPreview {
  links: {
    title: string
    link: string
  }[]
  itemsClassName?: ComponentProps<'div'>['className']
  className?: ComponentProps<'div'>['className']
}

const FooterLinksPreview = async ({ links, itemsClassName, className }: IFooterLinksPreview) => {
  return (
    <div className={cn('flex flex-col gap-4', className)}>
      {links.map((link) => (
        <Link key={link.title} href={link.link} className={itemsClassName}>
          {link.title}
        </Link>
      ))}
    </div>
  )
}

export { FooterLinksPreview }
