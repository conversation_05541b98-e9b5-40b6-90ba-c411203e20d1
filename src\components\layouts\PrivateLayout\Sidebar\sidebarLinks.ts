import { Routes } from '@/routes/routes'
import {
  AudioWaveform,
  Command,
  GalleryVerticalEnd,
  HistoryIcon,
  LayoutDashboardIcon,
  ListIcon,
  LucideIcon,
  PlusCircleIcon,
  UserIcon,
} from 'lucide-react'

export interface IMenuItem {
  icon?: LucideIcon
  title?: string
  url?: string
  onClick?: () => void
}

export interface ISidebarLink {
  title?: string
  url?: string
  icon?: LucideIcon
  groupName?: string
  children?: ISidebarLink[]
  menu?: IMenuItem[]
}

export interface ISidebarGroup {
  label?: string
  sidebarLink: ISidebarLink[]
}

// This is sample data.
export const data = {
  user: {
    name: 'geexar',
    email: 'https://geexar.com/',
    avatar:
      'https://geexar.com/wp-content/uploads/2025/01/cropped-%D9%87%D9%88%D9%8A%D8%A9-%D8%AC%D9%8A%D9%83%D8%B3%D8%B1-%D9%81%D8%A7%D9%8A%D9%86%D8%A7%D9%84-13-1-180x180.png',
  },
  teams: [
    {
      name: 'Acme Inc',
      logo: GalleryVerticalEnd,
      plan: 'Enterprise',
    },
    {
      name: 'Acme Corp.',
      logo: AudioWaveform,
      plan: 'Startup',
    },
    {
      name: 'Evil Corp.',
      logo: Command,
      plan: 'Free',
    },
  ],
}

export const sidebarGroups: ISidebarGroup[] = [
  {
    label: 'dashboard',
    sidebarLink: [
      {
        title: 'dashboard',
        icon: LayoutDashboardIcon,
        url: Routes.dashboard,
        groupName: 'dashboard',
        menu: [{ icon: HistoryIcon, title: 'history', url: Routes.history }],
      },
    ],
  },
  {
    label: 'admins',
    sidebarLink: [
      {
        title: 'admins',
        icon: UserIcon,
        groupName: 'admins',
        children: [
          {
            title: 'all_admins',
            url: Routes.admins.list,
            icon: ListIcon,
          },
          {
            title: 'add_admin',
            url: Routes.admins.add,
            icon: PlusCircleIcon,
          },
        ],
      },
    ],
  },
]
