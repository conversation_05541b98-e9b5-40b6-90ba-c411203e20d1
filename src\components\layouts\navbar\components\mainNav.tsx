'use client'

import Image, { StaticImageData } from 'next/image'
import Link from 'next/link'
import { useTranslations } from 'next-intl'
import { usePathname } from 'next/navigation'

import { useMemo } from 'react'

import { cn } from '@/lib/utils'

// Routes
import { Routes } from '@/routes/routes'

// navbar components
import { ProfileBtn } from '@/components/layouts/navbar/components/profileBtn'
import { LocaleToggle } from '@/components/core/LocaleToggle'
import { NotificationBtn } from '@/components/layouts/navbar/components/notificaionBtn'
import { MobileMenu } from '@/components/layouts/navbar/components/mobileMenu'

// images
import Logo from '/public/logo.webp'
import HomeIcon from '/public/icons/home_logo_nav.svg'
import ActiveHomeIcon from '/public/icons/home_logo_nav_active.svg'

export interface IMainNavItem {
  href: string
  label: string
  isActive: boolean
  image?: StaticImageData
}

function MainNav() {
  const t = useTranslations()
  const pathname = usePathname()

  const isActiveRoute = (route: string) => {
    const pathWithoutLocale = pathname.replace(/^\/[a-z]{2}/, '') || '/'
    return pathWithoutLocale === route
  }

  const mainNavItems = useMemo(
    () => [
      {
        href: Routes.HOME,
        label: t('nav.home'),
        isActive: isActiveRoute(Routes.HOME),
        image: isActiveRoute(Routes.HOME) ? ActiveHomeIcon : HomeIcon,
      },
      {
        href: Routes.ARTICLES,
        label: t('nav.articles'),
        isActive: isActiveRoute(Routes.ARTICLES),
      },
      {
        href: Routes.ACTIVITIES,
        label: t('nav.activities'),
        isActive: isActiveRoute(Routes.ACTIVITIES),
      },
      {
        href: Routes.FAQS,
        label: t('nav.faqs'),
        isActive: isActiveRoute(Routes.FAQS),
      },
      {
        href: Routes.CONTACT,
        label: t('nav.contact'),
        isActive: isActiveRoute(Routes.CONTACT),
      },
    ],
    [pathname, t]
  )

  return (
    <div className="container flex items-center justify-between gap-2 py-3">
      <Link href={Routes.HOME} className="flex justify-center items-center">
        <Image src={Logo} alt="logo" width={100} height={92} />
      </Link>

      <div className="flex items-center justify-center gap-3 flex-1 max-md:hidden">
        {mainNavItems.map((item) => (
          <Link
            key={item.href}
            href={item.href}
            className={cn(
              'text-primary-02 flex items-center justify-center gap-1 md:text-lg text-base',
              item.isActive && 'text-primary-01'
            )}
          >
            {item.image && <Image src={item.image} alt="home icon logo" width={24} height={24} />}
            {item.label}
          </Link>
        ))}
      </div>

      <div className="flex justify-between items-center min-w-fit gap-4">
        <ProfileBtn className="max-md:hidden min-w-[48px]" contentClassName="max-md:hidden" />
        <LocaleToggle className="max-md:hidden min-w-[48px]" contentClassName="max-md:hidden" />

        <NotificationBtn />

        {/* Mobile Menu */}
        <MobileMenu mobileNavItems={mainNavItems} />
      </div>
    </div>
  )
}

export { MainNav }
