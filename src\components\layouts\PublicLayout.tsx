import { Navbar } from '@/components/layouts/navbar'
import { Footer } from '@/components/layouts/footer'

interface PublicLayoutProps {
  children: React.ReactNode
}

const PublicLayout = ({ children }: PublicLayoutProps) => {
  return (
    <main className="flex min-h-screen flex-col items-center justify-between">
      <Navbar />

      {children}

      <Footer />
    </main>
  )
}

export default PublicLayout
