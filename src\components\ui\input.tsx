import { cn } from '@/lib/utils'
import type { TButtonProps } from './button'
import type { ReactElement, ComponentProps, ReactNode } from 'react'

interface Props extends Omit<ComponentProps<'input'>, 'prefix' | 'suffix'> {
  slim?: boolean
  suffix?: ReactNode
  prefix?: ReactNode
  containerClassName?: string
}

const iconStyles = 'h-full px-2 bg-transparent'

function Input({ className, type, slim, suffix, prefix, containerClassName, ...props }: Props) {
  return (
    <div
      className={cn(
        'h-fit flex items-center border-input rounded-md border dark:bg-input/30 bg-transparent',
        containerClassName
      )}
    >
      {prefix && (
        <div className={cn(iconStyles, (prefix as ReactElement<TButtonProps>)?.props.type == 'button' && 'py-0 px-0')}>
          {prefix}
        </div>
      )}
      <input
        type={type}
        data-slot="input"
        className={cn(
          'file:text-foreground placeholder:capitalize placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground flex h-9 w-full min-w-0 px-2 py-1 bg-transparent text-base transition-[color] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',
          'aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive',
          className,
          slim && 'rounded-s-none'
        )}
        {...props}
      />
      {suffix && (
        <div className={cn(iconStyles, (suffix as ReactElement<TButtonProps>)?.props.type == 'button' && 'py-0 px-0')}>
          {suffix}
        </div>
      )}
    </div>
  )
}

export { Input }
