import { apiService } from '@/services'

// footer components
import { BottomFooter } from '@/components/layouts/footer/components/bottomFooter'
import TopFooter from '@/components/layouts/footer/components/topFooter'
import { FooterContent } from '@/components/layouts/footer/components/footerContent'

// images
import BG from '/public/images/footer_nav_bg.png'

async function Footer({ hasImg = true }: { hasImg?: boolean }) {
  const doctorInfo = await apiService({
    path: 'doctor-info',
    revalidate: 1800, // Cache for 24 hours - enables static generation
  })

  return (
    <footer className=" w-full">
      {hasImg && <TopFooter />}

      <section className="relative w-full overflow-hidden  pt-10 ">
        <div className="absolute inset-0 base_gradient py-10" />

        <div
          className="absolute inset-0 z-0"
          style={{
            backgroundImage: `url(${BG.src})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            backgroundRepeat: 'no-repeat',
          }}
        />

        <div className="relative z-10">
          <FooterContent doctorInfo={doctorInfo?.data} />
          <BottomFooter doctorInfo={doctorInfo?.data} />
        </div>
      </section>
    </footer>
  )
}

export { Footer }
