import { Accordion, AccordionItem, AccordionTrigger, AccordionContent } from '@/components/ui/according'
import { IFaqs } from '@/types/faqs'

function FaqsItem({ question, answer }: IFaqs) {
  return (
    <Accordion type="single" collapsible className="w-full bg-primary-bg rounded-3xl p-4">
      <AccordionItem value="item-1">
        <AccordionTrigger className="text-primary-03 lg:text-[28px] md:text-[24px] sm:text-xl text-lg">
          {question}
        </AccordionTrigger>
        <AccordionContent className="text-gray-01 lg:text-[24px] md:text-xl sm:text-lg text-base border-t border-[#D2D2D2]  pt-4">
          {answer}
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  )
}

export { FaqsItem }
