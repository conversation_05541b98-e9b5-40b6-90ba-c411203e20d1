import {
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from '@/components/ui/sidebar'
import { useRtl } from '@/hooks/useRtl'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@radix-ui/react-collapsible'
import { ChevronLeft, ChevronRight } from 'lucide-react'
import { Link, matchPath, useLocation } from 'react-router-dom'
import { ISidebarLink } from './sidebarLinks'
import { useTranslation } from 'react-i18next'
import { SidebarLinkWithMenu } from './SidebarLinkWithMenu'
import { cn } from '@/lib/utils'

export const SidebarLinkWithChildren = ({ item }: { item: ISidebarLink }) => {
  const { isRtl } = useRtl()
  const { t } = useTranslation()
  const location = useLocation()
  const isActive = item.groupName && !!matchPath({ path: item.groupName || '', end: false }, location.pathname)

  return (
    <Collapsible key={item.title} asChild defaultOpen={true} className="group/collapsible">
      <SidebarMenuItem>
        <CollapsibleTrigger asChild className={cn('cursor-pointer', isActive && 'bg-primary/20')}>
          <SidebarMenuButton tooltip={item.title}>
            {item.icon && <item.icon />}
            {item.title && <span>{t(`navbar.${item.title}`)}</span>}
            {isRtl ? (
              <ChevronLeft className="ms-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-[-90deg]" />
            ) : (
              <ChevronRight className="ms-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
            )}
          </SidebarMenuButton>
        </CollapsibleTrigger>
        <CollapsibleContent>
          <SidebarMenuSub>
            {item.children?.map((child) => {
              if (child.children) return <SidebarLinkWithChildren item={child} />
              if (child.menu) return <SidebarLinkWithMenu item={child} />
              return <SidebarGroupItem key={child.title} item={child} />
            })}
          </SidebarMenuSub>
        </CollapsibleContent>
      </SidebarMenuItem>
    </Collapsible>
  )
}

const SidebarGroupItem = ({ item }: { item: ISidebarLink }) => {
  const { t } = useTranslation()
  const location = useLocation()
  const isActive = !!matchPath({ path: item.url || '', end: true }, location.pathname)

  return (
    <SidebarMenuSubItem key={item.title}>
      <SidebarMenuSubButton asChild className={cn(isActive && 'bg-primary/20')}>
        <Link to={item.url || ''}>
          {item.icon && <item.icon />}
          <span>{t(`navbar.${item.title}`)}</span>
        </Link>
      </SidebarMenuSubButton>
    </SidebarMenuSubItem>
  )
}
