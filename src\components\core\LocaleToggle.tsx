'use client'

import { ComponentProps, useCallback } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { useLocale, useTranslations } from 'next-intl'
import { Button } from '@/components/ui/button'

import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '../ui/dropdown-menu'
import { Globe } from 'lucide-react'
import { cn } from '@/lib/utils'

const LocaleToggle = ({
  className,
  contentClassName,
}: {
  className?: ComponentProps<'div'>['className']
  contentClassName?: ComponentProps<'div'>['className']
}) => {
  const t = useTranslations()
  const locale = useLocale()
  const router = useRouter()
  const pathname = usePathname()

  const handleSwitchLocale = useCallback(
    (newLocale: 'ar' | 'en') => {
      // Remove the current locale from the pathname
      const pathnameWithoutLocale = pathname.replace(`/${locale}`, '')

      // Navigate to the new locale
      router.push(`/${newLocale}${pathnameWithoutLocale}`)
    },
    [locale, pathname, router]
  )

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon" className={cn('bg-transparent flex justify-center gap-1 items-center text-primary-03 font-normal', className)}>
          {locale === 'ar' && t('nav.arabic')}
          {locale === 'en' && t('nav.english')}
          <Globe className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className={cn('', contentClassName)}>
        <DropdownMenuItem onClick={() => handleSwitchLocale('en')}>EN</DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleSwitchLocale('ar')}>AR</DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

export { LocaleToggle }
