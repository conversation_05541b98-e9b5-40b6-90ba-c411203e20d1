import { Outlet } from 'react-router'
import { SidebarInset, SidebarProvider } from '@/components/ui/sidebar'
import { PrivateHeader } from '@/components/layouts/PrivateLayout/Header'
import { AppSidebar } from '@/components/layouts/PrivateLayout/Sidebar/app-sidebar'
import { Suspense } from 'react'
import Loading from '@/components/core/Loading'

const PrivateLayout = () => {
  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <PrivateHeader />
        <Suspense
          fallback={
            <div className="h-full w-full flex items-center justify-center">
              <Loading />
            </div>
          }
        >
          <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
            <Outlet />
          </div>
        </Suspense>
      </SidebarInset>
    </SidebarProvider>
  )
}

export default PrivateLayout
