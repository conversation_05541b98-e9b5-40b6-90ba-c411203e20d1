// ** Next **
import { Zain } from 'next/font/google'

import type { Metadata } from 'next'
// ** Styles **
import '@/styles/globals.css'

// ** I18N **
import { getMessages, setRequestLocale } from 'next-intl/server'

import { getServerAuthSession } from '@/config/auth'
import { i18n } from '@/i18n-config'

// ** Providers **
import Providers from './providers'

import type { Locale } from '@/i18n-config'

// ** Font **
const zain = Zain({
  subsets: ['latin', 'arabic'], // ضروري لدعم العربية
  weight: ['300', '400', '700'], // اختار الوزن المناسب فقط لتقليل حجم البندل
  display: 'swap',
  variable: '--font-zain',
})

// ** Metadata **
export const metadata: Metadata = {
  title: {
    template: '%s | <App_Name>',
    default: '<App_Name>',
  },
  description: 'Write a description for your website',
}

// ** Generate Static Params **
export async function generateStaticParams() {
  return i18n.locales.map((locale) => ({ lang: locale }))
}

interface IProps {
  children: React.ReactNode
  params: Promise<{ lang: Locale }>
}

export default async function RootLayout({ children, params }: Readonly<IProps>) {
  const resolvedParams = await params

  // Enable static rendering by setting the request locale
  setRequestLocale(resolvedParams.lang)

  const messages = await getMessages()
  const session = await getServerAuthSession()

  return (
    <html lang={resolvedParams.lang} dir={resolvedParams.lang === 'ar' ? 'rtl' : 'ltr'} suppressHydrationWarning>
      <body className={zain.className} suppressHydrationWarning>
        <Providers {...{ session, locale: resolvedParams.lang, messages }}>{children}</Providers>
      </body>
    </html>
  )
}
