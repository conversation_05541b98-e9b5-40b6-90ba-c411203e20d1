'use client'

import { useRouter } from 'next/navigation'
import { useTranslations } from 'next-intl'

// ui components
import { But<PERSON> } from '@/components/ui/button'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { Bell } from 'lucide-react'
import { cn } from '@/lib/utils'
import { ComponentProps } from 'react'

const NotificationBtn = ({
  className,
  contentClassName,
}: {
  className?: ComponentProps<'div'>['className']
  contentClassName?: ComponentProps<'div'>['className']
}) => {
  const router = useRouter()
  const t = useTranslations()
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild className={cn('bg-primary-01-bg rounded-full size-11', className)}>
        <Button variant="outline" size="icon" className={cn('bg-primary-01-bg rounded-full size-11')}>
          <Bell />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className={cn('', contentClassName)}>
        <DropdownMenuItem>{t('nav.notifications')}</DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

export { NotificationBtn }
