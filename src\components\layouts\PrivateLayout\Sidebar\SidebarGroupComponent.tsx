'use client'

import { SidebarGroup, SidebarGroupLabel, SidebarMenu } from '@/components/ui/sidebar'
import { ISidebarGroup } from './sidebarLinks'
import { SidebarLinkWithMenu } from './SidebarLinkWithMenu'
import { SidebarLinkWithChildren } from './SidebarLinkWithChildren'
import { useTranslation } from 'react-i18next'

export function SidebarGroupComponent({ group }: { group: ISidebarGroup }) {
  const { t } = useTranslation()

  return (
    <SidebarGroup>
      {group.label && <SidebarGroupLabel>{t(`navbar.${group.label}`)}</SidebarGroupLabel>}
      <SidebarMenu>
        {group.sidebarLink.map((item) =>
          item.url ? <SidebarLinkWithMenu item={item} /> : <SidebarLinkWithChildren item={item} />
        )}
      </SidebarMenu>
    </SidebarGroup>
  )
}
