import { useFormContext } from 'react-hook-form'

// ui components
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Textarea } from '@/components/ui/textarea'

interface FormTextAreaProps extends Omit<React.ComponentProps<'textarea'>, 'onChange'> {
  name: string
  label?: string
  onChange?: (newValue: string) => void
  containerClassName?: string
}

export function FormTextArea({ name, label, onChange, containerClassName, ...props }: FormTextAreaProps) {
  const { control } = useFormContext()

  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem className={containerClassName}>
          {label && <FormLabel>{label}</FormLabel>}
          <FormControl>
            <Textarea
              {...field}
              onChange={(newValue) => {
                field.onChange(newValue)
                onChange && onChange(newValue.target.value)
              }}
              {...props}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  )
}
