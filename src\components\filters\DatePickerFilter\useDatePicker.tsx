import { format } from 'date-fns'
import { useTranslation } from 'react-i18next'
import { useQuery } from '@/store/queryContext/useQueryContext'
import { isDateRange } from 'react-day-picker'

import type {
  DatePickerHookReturn,
  IDatePickerProps,
  TDatePickerValueTemplate,
} from '@/components/filters/DatePickerFilter/types'
import { DatePickerValue } from '@/components/ui/datePicker/types'

export default function useDatePicker({ queryName, mode = 'range' }: IDatePickerProps): DatePickerHookReturn {
  const { t } = useTranslation()
  const { forwardAddQuery, forwardDeleteQuery, forwardQuery } = useQuery()

  const handleSelect = (value: DatePickerValue) => {
    if (!value) return

    const formatted = formatValue(value)
    setQueryValue(formatted)
  }

  const formatValue = (value: DatePickerValue): TDatePickerValueTemplate => {
    if (mode === 'range' && isDateRange(value)) {
      const from = value?.from ? format(value.from, 'yyyy-MM-dd') : ''
      const to = value?.to ? format(value.to, 'yyyy-MM-dd') : ''
      return `${from} - ${to}`
    }

    if (mode === 'multiple' && Array.isArray(value)) {
      return value.map((day) => format(day, 'yyyy-MM-dd'))
    }

    // single mode
    return format(value as Date, 'yyyy-MM-dd')
  }

  const setQueryValue = (formatted: TDatePickerValueTemplate) => {
    if (mode === 'range' && typeof formatted === 'string') {
      const [from, to] = formatted.split(' - ')
      const { start, end } = queryName as { start: string; end: string }
      if (from && to) {
        forwardAddQuery({
          [start]: from,
          [end]: to,
        })
      }
    } else if (typeof queryName === 'string') {
      const value = Array.isArray(formatted) ? formatted.join(',') : (formatted as string)
      forwardAddQuery({ [queryName]: value })
    }
  }

  const removeQueryFilter = () => {
    if (mode === 'range') {
      const { start, end } = queryName as { start: string; end: string }
      forwardDeleteQuery(start)
      forwardDeleteQuery(end)
    } else if (typeof queryName === 'string') {
      forwardDeleteQuery(queryName)
    }
  }
  const getDefaultValue = () => {
    if (!forwardQuery || Object.keys(forwardQuery).length === 0) return undefined

    if (mode === 'range' && typeof queryName === 'object') {
      const { start, end } = {
        start: forwardQuery?.[queryName.start],
        end: forwardQuery?.[queryName.end],
      }

      if (!start && !end) return undefined

      return {
        from: start ? new Date(start) : undefined,
        to: end ? new Date(end) : undefined,
      }
    }
    return undefined
  }

  return {
    t,
    handleSelect,
    removeQueryFilter,
    getDefaultValue,
  }
}
