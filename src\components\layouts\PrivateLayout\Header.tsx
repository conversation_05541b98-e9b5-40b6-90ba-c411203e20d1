import { Separator } from '@/components/ui/separator'
import { SidebarTrigger } from '@/components/ui/sidebar'
import { LocaleToggle } from '@/components/core/LocaleToggle'
import { ThemeToggle } from '@/components/theme/ThemeToggle'
import { Breadcrumb } from '@/components/ui/breadcrumb'

const PrivateHeader = () => {
  return (
    <header className="flex h-16 shrink-0 items-center justify-between gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12">
      <section className="flex items-center gap-2 px-4">
        <SidebarTrigger className="-ml-1" />
        <Separator orientation="vertical" className="mr-2 data-[orientation=vertical]:h-4" />

        {/* Breadcrumb section */}
        <Breadcrumb />
      </section>
      <section className="flex items-center gap-2 px-4">
        <LocaleToggle />
        <Separator orientation="vertical" className="mx-1 data-[orientation=vertical]:h-4" />
        <ThemeToggle />
      </section>
    </header>
  )
}

export { PrivateHeader }
