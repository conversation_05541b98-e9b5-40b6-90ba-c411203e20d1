import { ComponentProps, ReactNode } from 'react'

import { cn } from '@/utils/cn'

// types
import { ISocialMedia } from '@/types/doctorInfo'

// icons
import { InstgramIcon } from '@/components/icons/social/InstgramIcon'
import { FacebookIcon } from '@/components/icons/social/FacebookIcon'
import { XIcon } from '@/components/icons/social/XIcon'
import { WhatsAppIcon } from '@/components/icons/social/WhatsAppIcon'

interface ISocialLinks extends ISocialMedia {
  whatsapp: string
  className?: ComponentProps<'div'>['className']
}

function SocialLinks({ whatsapp, facebook, twitter, instagram, className }: ISocialLinks) {
  const iconContainerClassName: ComponentProps<'div'>['className'] = 'size-full'

  const socialItems = [
    {
      link: whatsapp ? `https://wa.me/${whatsapp.replace(/\D/g, '')}` : '#',
      icon: <WhatsAppIcon className={iconContainerClassName} />,
    },
    {
      link: twitter,
      icon: <XIcon className={iconContainerClassName} />,
    },
    {
      link: instagram,
      icon: <InstgramIcon className={iconContainerClassName} />,
    },
    {
      link: facebook,
      icon: <FacebookIcon className={iconContainerClassName} />,
    },
  ]

  return (
    <div className={cn('flex items-center justify-between gap-4', className)}>
      {socialItems.map((item, index) => (
        <SocialItem key={`${item.link}-${index}`} link={item.link} icon={item.icon} />
      ))}
    </div>
  )
}

const SocialItem = ({ link, icon }: { link: string; icon: ReactNode }) => {
  return (
    <a href={link} target="_blank" rel="noopener noreferrer" className="size-8 ">
      {icon}
    </a>
  )
}

export { SocialLinks }
