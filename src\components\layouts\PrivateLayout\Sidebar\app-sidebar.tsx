import { SidebarGroupComponent } from '@/components/layouts/PrivateLayout/Sidebar/SidebarGroupComponent'
import { NavUser } from '@/components/layouts/PrivateLayout/Sidebar/nav-user'
import { TeamSwitcher } from '@/components/layouts/PrivateLayout/Sidebar/team-switcher'
import { Sidebar, SidebarContent, SidebarFooter, SidebarHeader, SidebarRail } from '@/components/ui/sidebar'
import { data, sidebarGroups } from './sidebarLinks'

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar collapsible="icon" {...props} className="start-0">
      <SidebarHeader>
        <TeamSwitcher teams={data.teams} />
      </SidebarHeader>
      <SidebarContent>
        {sidebarGroups.map((group, index) => (
          <SidebarGroupComponent key={index} group={group} />
        ))}
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={data.user} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}
