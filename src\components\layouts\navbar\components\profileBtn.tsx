'use client'

import { useRouter } from 'next/navigation'
import { useTranslations } from 'next-intl'

// ui components
import { But<PERSON> } from '@/components/ui/button'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { User } from 'lucide-react'
import { ComponentProps } from 'react'
import { cn } from '@/lib/utils'

const ProfileBtn = ({
  className,
  contentClassName,
}: {
  className?: ComponentProps<'div'>['className']
  contentClassName?: ComponentProps<'div'>['className']
}) => {
  const router = useRouter()
  const t = useTranslations()
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild className={cn('bg-primary-01-bg rounded-full size-11', className)}>
        <Button variant="outline" size="icon" className="bg-primary-01-bg rounded-full size-11">
          <User />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className={cn('', contentClassName)}>
        <DropdownMenuItem onClick={() => router.push('/bookings')}>{t('nav.bookings')}</DropdownMenuItem>
        <DropdownMenuItem onClick={() => router.push('/profile')}>{t('nav.profile')}</DropdownMenuItem>
        <DropdownMenuItem onClick={() => console.log('/logout')}>{t('nav.logout')}</DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

export { ProfileBtn }
