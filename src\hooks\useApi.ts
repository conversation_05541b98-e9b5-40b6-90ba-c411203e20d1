'use client'
import { actionService, ActionServiceProps } from '@/services/actionService'
import { ActionServiceReturn } from '@/types'
import { useActionState } from 'react'

interface Props extends ActionServiceProps {
  handleError?: boolean
  handleSuccess?: boolean
}

interface ApiReturn<T> {
  state: ActionServiceReturn<T>
  action: (payload: any) => void
  isPending: boolean
}

const useApi = <T>({ handleError = true, handleSuccess = true, ...props }: Props): ApiReturn<T> => {
  const serverAction = actionService.bind(null, props)
  const [state, action, isPending] = useActionState(serverAction, { status: false })

  // if handleError true can useError

  return { state: state as ActionServiceReturn<T>, action, isPending }
}

export default useApi
