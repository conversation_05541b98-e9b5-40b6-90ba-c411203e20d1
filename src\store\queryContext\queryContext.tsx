'use client'

import { createContext, useState } from 'react'
import { useRouter, usePathname, useSearchParams } from 'next/navigation'
import type { IQueryContextProps, IQueryProviderProps } from './types'

export const defaultValues: IQueryContextProps = {
  routeQuery: false,
  query: null,
  addQuery: () => {},
  removeQuery: () => {},
  addRouteQuery: () => {},
  removeRouteQuery: () => {},
  forwardAddQuery: () => {},
  forwardDeleteQuery: () => {},
  resetAllQueries: () => {},
  resetAllRouteQueries: () => {},
  forwardResetAllQueries: () => {},
  forwardQuery: {},
}

export const QueryContext = createContext<IQueryContextProps>(defaultValues)

const QueryProvider = ({ children, isRouteQuery = true }: IQueryProviderProps) => {
  const router = useRouter()
  const pathname = usePathname()
  const searchParams = useSearchParams()
  const [query, setQuery] = useState<Record<string, string> | null>(null)

  const updateRouteParams = (params: Record<string, string>) => {
    const merged = { ...Object.fromEntries(searchParams.entries()), ...params }
    const queryString = new URLSearchParams(merged).toString()
    router.replace(`${pathname}?${queryString}`)
  }

  const addQuery = (newQuery: Record<string, string>) => {
    const previousQueries = query
    previousQueries?.page && delete previousQueries.page
    setQuery({ ...previousQueries, ...newQuery })
  }

  const removeQuery = (queryName: string | string[]) => {
    const queryObject = { ...query }
    if (Array.isArray(queryName)) {
      queryName.forEach((q) => delete queryObject[q])
    } else {
      delete queryObject[queryName]
    }
    delete queryObject.page
    setQuery(Object.keys(queryObject).length ? queryObject : null)
  }

  const addRouteQuery = (name: string, value: string) => {
    const params = { ...Object.fromEntries(searchParams.entries()) }
    if (params.page && name !== 'page') delete params.page
    updateRouteParams({ ...params, [name]: value })
  }

  const removeRouteQuery = (queryName: string | string[]) => {
    const params = { ...Object.fromEntries(searchParams.entries()) }
    if (Array.isArray(queryName)) {
      queryName.forEach((q) => delete params[q])
    } else {
      delete params[queryName]
    }
    updateRouteParams(params)
  }

  const resetAllQueries = () => setQuery(null)

  const resetAllRouteQueries = () => router.replace(pathname)

  const forwardAddQuery = isRouteQuery ? updateRouteParams : addQuery
  const forwardDeleteQuery = isRouteQuery ? removeRouteQuery : removeQuery
  const forwardResetAllQueries = isRouteQuery ? resetAllRouteQueries : resetAllQueries

  const objOfSearchParams = Object.fromEntries(searchParams.entries())
  const forwardQuery = isRouteQuery ? objOfSearchParams : query

  return (
    <QueryContext.Provider
      value={{
        routeQuery: isRouteQuery,
        query,
        addQuery,
        removeQuery,
        addRouteQuery,
        removeRouteQuery,
        forwardAddQuery,
        forwardDeleteQuery,
        forwardResetAllQueries,
        resetAllRouteQueries,
        resetAllQueries,
        forwardQuery,
      }}
    >
      {children}
    </QueryContext.Provider>
  )
}

export default QueryProvider
