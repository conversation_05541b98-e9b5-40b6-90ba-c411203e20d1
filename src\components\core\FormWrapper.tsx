import { yupResolver } from '@hookform/resolvers/yup'
import {
  Children,
  cloneElement,
  createContext,
  isValidElement,
  PropsWithChildren,
  Ref,
  useContext,
  useImperativeHandle,
} from 'react'
import { FieldErrors, FormProvider, useForm, UseFormReturn } from 'react-hook-form'
import { Form } from '@/components/ui/form'
import { InferType } from 'yup'
// import useSetFormErrors from '@/hooks/useSetFormErrors';

interface Props<T> {
  schema: any
  onSubmit: (data: T) => void
  defaultValues: T
  ref?: Ref<any>
}

interface IFormContext {
  errors: FieldErrors
}

export interface IFormWrapper extends UseFormReturn {
  setValues: (values: any) => void
  // setErrors: (errors: IResponseError['errors']) => void
}

export const FormContext = createContext<IFormContext | undefined>(undefined)

export const useFormWrapperContext = () => {
  const context = useContext(FormContext)
  if (!context?.errors) throw new Error('Please provide context')
  return { errors: context.errors }
}

export const FormWrapper = <T,>({ schema, onSubmit, defaultValues, children, ref }: PropsWithChildren<Props<T>>) => {
  // const { mapErrorsToFields } = useSetFormErrors()

  const methods = useForm<InferType<typeof schema>>({
    resolver: yupResolver(schema),
    defaultValues: { ...defaultValues },
    shouldFocusError: false,
    mode: 'all',
  })

  useImperativeHandle(ref, () => ({
    ...methods,
    // setErrors: (errors: IResponseError['errors']) => {
    //   mapErrorsToFields(errors, methods.setError)
    // },
    setValues: (values: InferType<typeof schema>) => {
      for (const key in values) {
        methods.setValue(key, values[key])
      }
    },
  }))

  console.table(methods.getValues())
  console.table(methods.formState.errors)

  return (
    <FormProvider {...methods}>
      <Form {...methods}>
        <form onSubmit={methods.handleSubmit(onSubmit)}>
          <FormContext.Provider value={{ errors: methods.formState.errors }}>
            {Children.map(children, (child) => (isValidElement(child) ? cloneElement<any>(child) : child))}
          </FormContext.Provider>
        </form>
      </Form>
    </FormProvider>
  )
}
