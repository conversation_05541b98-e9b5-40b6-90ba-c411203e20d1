'use client'

import { Suspense } from 'react'
import { usePathname } from 'next/navigation'
import Loading from '@/components/core/Loading'
import { ThemeProvider } from '@/components/theme/ThemeProvider'

interface AppLayoutProps {
  children: React.ReactNode
}

const AppLayout = ({ children }: AppLayoutProps) => {
  const pathname = usePathname()

  return (
    <ThemeProvider defaultTheme="dark" storageKey="vite-ui-theme">
      <div className="min-h-screen min-w-full flex items-center justify-center">
        <Suspense fallback={<Loading />} key={pathname}>
          {children}
        </Suspense>
      </div>
    </ThemeProvider>
  )
}

export default AppLayout
