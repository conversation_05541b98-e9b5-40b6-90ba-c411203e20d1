import FilterSelect from '@/components/filters/FilterSelect'
import SearchFilter from '@/components/filters/SearchFilter'
import { SearchIcon } from 'lucide-react'
import { useTranslations } from 'next-intl'

const orderOptions = [
  { label: 'All', value: 'all' },
  { label: 'Newest', value: 'newest' },
  { label: 'Oldest', value: 'oldest' },
]

const BlogFilters = () => {
  const t = useTranslations()
  return (
    <div className="flex justify-between gap-4 w-full">
      <SearchFilter name="search" placeholder={t('label.search_here')} prefix={<SearchIcon />} />
      <div className="flex">
        <FilterSelect
          name="order"
          label={t('label.order_by')}
          placeholder={t('label.select_order')}
          data={orderOptions}
          valueKey="value"
          labelKey="label"
        />
      </div>
    </div>
  )
}

export default BlogFilters
