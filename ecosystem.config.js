module.exports = {
  apps: [
    {
      script: 'yarn',
      args: 'start',
      name: 'base-next',
      instances: '2', // Automatically set to the number of available CPU cores
      exec_mode: 'cluster', // Cluster mode for better performance and scalability
      autorestart: true, // Automatically restart the app on failure
      watch: true, // Enable to restart on code changes (useful in dev environment)
      // max_memory_restart: '500M', // Restart the app if memory exceeds 500MB
      env: {
        watch: false, // In production, better to keep watch OFF
        PORT: 3161 || 3262, // 👈 Set your custom port here
        NODE_ENV: 'production', // Best practice for production
      },
    },
  ],
};

