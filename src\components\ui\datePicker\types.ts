import { DateRange, Mode } from 'react-day-picker'

export type DatePickerValue =
  | Date // for 'single'
  | Date[] // for 'multiple'
  | DateRange // for 'range'
  | undefined // undefined by default

export interface DatePickerProps {
  label?: string
  placeholder?: string
  defaultValue?: DatePickerValue
  value?: DatePickerValue
  onChange?: (date: DatePickerValue) => void
  mode?: Mode
  className?: string
  onClear?: () => void
}
