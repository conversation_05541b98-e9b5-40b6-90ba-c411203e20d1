import { TextEditor, TextEditorProps } from '@/components/ui/TextEditor'
import { FormItem, FormField, FormControl, FormMessage, FormLabel } from '@/components/ui/form'

import { useFormContext } from 'react-hook-form'

export interface FormTextEditorProps extends TextEditorProps {
  name: string
  label?: string
  onChange?: (value: string) => void
}

export function FormTextEditor({ className, label, placeholder, name, onChange, ...props }: FormTextEditorProps) {
  const { control } = useFormContext()
  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem>
          {label && <FormLabel>{label}</FormLabel>}
          <FormControl className="h-fit">
            <TextEditor
              onChange={(value: string) => {
                field.onChange(value)
                onChange && onChange(value)
              }}
              name={name}
              {...(field as any)}
              {...props}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  )
}
