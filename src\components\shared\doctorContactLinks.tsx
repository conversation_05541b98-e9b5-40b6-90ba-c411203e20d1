import { ComponentProps } from 'react'
import { IDoctorInfo } from '@/types/doctorInfo'

import { cn } from '@/lib/utils'

// icons
import { LocationIcon } from '@/components/icons/social/LocationIcon'
import { EmailIcon } from '@/components/icons/social/EmailIcon'
import { WhatsAppWithBg } from '@/components/icons/social/WhatsAppWithBg'
import { PhoneIcon } from 'lucide-react'

interface Props extends Pick<IDoctorInfo, 'email' | 'phone' | 'whatsapp' | 'clinics'> {
  title?: string
  className?: ComponentProps<'div'>['className']
  titleClassName?: ComponentProps<'h5'>['className']
  listClassName?: ComponentProps<'ul'>['className']
  itemClassName?: ComponentProps<'li'>['className']
}

const DoctorContactLinks = ({
  email,
  phone,
  whatsapp,
  clinics,
  listClassName,
  itemClassName,
  title,
  titleClassName,
  className,
}: Props) => {
  const contactItems = [
    {
      link: `mailto:${email}`,
      icon: <EmailIcon className="h-4 " />,
      type: 'email',
      text: email,
    },
    {
      link: `tel:${phone}`,
      icon: <PhoneIcon className="h-4 fill-gray-01 stroke-gray-01" />,
      type: 'phone',
      text: phone,
    },
    {
      link: whatsapp ? `https://wa.me/${whatsapp.replace(/\D/g, '')}` : '#',
      icon: <WhatsAppWithBg className="size-4 fill-gray-01" />,
      type: 'whatsapp',
      text: whatsapp,
    },
  ]

  return (
    <div className={cn('flex flex-col lg:gap-6 md:gap-4 gap-3', className)}>
      {title && <h5 className={cn('text-primary-01 font-bold md:text-lg text-base', titleClassName)}>{title}</h5>}
      <ul className={cn('list-inside lg:space-y-6 md:space-y-4 space-y-3', listClassName)}>
        {contactItems.map((item) => (
          <>
            {item.text && (
              <li
                key={item.link}
                className={cn('text-primary-03 text-lg flex justify-start items-center gap-1', itemClassName)}
              >
                {item.icon}
                <a
                  href={item.link}
                  target="_blank"
                  rel="noopener noreferrer"
                  className={cn(item.type === 'phone' ? 'ltr' : 'rtl')}
                >
                  {item.text}
                </a>
              </li>
            )}
          </>
        ))}

        {/* list of clinics */}
        {clinics && clinics.length > 0 && (
          <li className={cn('text-primary-03 text-lg flex justify-start items-start gap-3', itemClassName)}>
            <LocationIcon className="h-4 mt-1" />

            <ul className="list-inside list-disc">
              {clinics.map((clinic) => (
                <li key={clinic.name}>
                  <a
                    href={clinic.location}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-primary-03 text-lg hover:underline"
                  >
                    {clinic.name}
                  </a>
                </li>
              ))}
            </ul>
          </li>
        )}
      </ul>
    </div>
  )
}

export { DoctorContactLinks }
