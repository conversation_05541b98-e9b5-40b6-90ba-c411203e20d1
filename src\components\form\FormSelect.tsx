import { useFormContext } from 'react-hook-form'

import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { InputHTMLAttributes } from 'react'
import { useRtl } from '@/hooks/useRtl'

interface FormSelectProps<T> extends Omit<InputHTMLAttributes<HTMLInputElement>, 'onChange'> {
  data: T[]
  name: string
  valueKey: keyof T
  labelKey: keyof T
  label?: string
  placeholder?: string
  onChange?: (newValue: T) => void
}

export function FormSelect<T>({ name, data, labelKey, valueKey, label, placeholder, onChange }: FormSelectProps<T>) {
  const { control } = useFormContext()
  const { isRtl } = useRtl()

  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem>
          {label && <FormLabel>{label}</FormLabel>}
          <Select
            defaultValue={field.value}
            onValueChange={(newValue) => {
              field.onChange(newValue)
              onChange && onChange(newValue as T)
            }}
          >
            <FormControl>
              <SelectTrigger className="w-full" dir={isRtl ? 'rtl' : 'ltr'}>
                <SelectValue placeholder={placeholder} />
              </SelectTrigger>
            </FormControl>
            <SelectContent dir={isRtl ? 'rtl' : 'ltr'}>
              {data &&
                data.length &&
                data.map((item, index) => (
                  <SelectItem key={`${index}-${item[valueKey]}`} value={item[valueKey] as string}>
                    {item[labelKey] as string}
                  </SelectItem>
                ))}
            </SelectContent>
          </Select>
          <FormMessage />
        </FormItem>
      )}
    />
  )
}
