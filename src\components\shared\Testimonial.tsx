import Image from 'next/image'
import starIcon from '/public/icons/star.svg'
import grayStarIcon from '/public/icons/gray-star.svg'
import { ITestimonial } from '@/types'

function truncateWords(text: string, limit: number): string {
  if (!text) return 'No title'
  const words = text.split(' ')
  return words.length > limit ? words.slice(0, limit).join(' ') + '...' : text
}

const Testimonial = ({ title, rating, name }: ITestimonial) => {
  const limitedTitle = truncateWords(title, 18)

  return (
    <div className="bg-white basic_card_shadow max-w-[370px] rounded-[24px] p-8">
      <div className="flex items-top gap-2">
        <span className="flex gap-1">
          {Array.from({ length: 5 }, (_, index) => (
            <Image
              key={index}
              src={index < rating ? starIcon : grayStarIcon}
              alt="testimonial star"
              width={16}
              height={16}
            />
          ))}
        </span>
        <span className="mt-[5px] font-bold text-xl text-gray-01">({rating}/5)</span>
      </div>
      <h3 className="text-[28px] text-primary-03">{limitedTitle}</h3>
      <span className="mt-[5px] font-bold text-xl text-gray-01">{name}</span>
    </div>
  )
}

export default Testimonial
